package api

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	pb "github.com/vendasta/generated-protos-go/yext/v1"
	listingproductsresources "github.com/vendasta/iam-resources/applications/listing-products"
	"github.com/vendasta/listing-products/internal/utils"
	"google.golang.org/grpc"

	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	accounts "github.com/vendasta/accounts/sdks/go/v1"
	"github.com/vendasta/gosdks/statsd"
	"google.golang.org/protobuf/types/known/timestamppb"

	businessprofilefieldstatusservice "github.com/vendasta/listing-products/internal/businessprofilefieldstatus/service"
	listingprofilelegacy "github.com/vendasta/listing-products/internal/listingprofile/legacy"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	uberallhttpclient "github.com/vendasta/listing-products/internal/syndication/uberall/httpclient"

	"github.com/vendasta/listing-products/internal/constants"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/empty"
	"github.com/vendasta/IAM/sdks/go/v2/policy"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/validation"
	"github.com/vendasta/gosdks/validation/rules"
	"github.com/vendasta/gosdks/verrors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"golang.org/x/sync/errgroup"

	"github.com/vendasta/listing-products/internal/authservice"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
)

//go:generate mockgen -destination mock_service.go -source=listing_profile_server.go -package=api
type YextInterface interface {
	SyncAccountGroupToYext(ctx context.Context, in *pb.SyncAccountGroupToYextRequest, out ...grpc.CallOption) (*empty.Empty, error)
}

type ListingProfileServer struct {
	service               listingprofileservice.Interface
	authService           authservice.Interface
	listingProfileAdapter *ListingProfileAdapter
	uberallHttpClient     uberallhttpclient.HttpClienter
	accountGroup          accountgroup.Interface
	bpfsService           businessprofilefieldstatusservice.Service
	accountsService       accounts.Interface
	seoDataService        seodataservice.Service
	seoSettingsService    seosettingsservice.Service
}

func (l *ListingProfileServer) GetDoctorDotComCategories(ctx context.Context, request *listing_products_v1.GetDoctorDotComCategoriesRequest) (*listing_products_v1.GetDoctorDotComCategoriesResponse, error) {
	categories, err := l.uberallHttpClient.GetDoctorDotComCategories(ctx)
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetDoctorDotComCategoriesResponse{
		Categories: categories,
	}, nil
}

func (l *ListingProfileServer) GetMoreHoursTypes(ctx context.Context, req *listing_products_v1.GetMoreHoursTypesRequest) (*listing_products_v1.GetMoreHoursTypesResponse, error) {
	err := validation.NewValidator().Rule(rules.StringNotEmpty(req.GetBusinessId(), "business_id is required")).Validate()
	if err != nil {
		return nil, err
	}
	languageCode := req.GetLanguageCode()
	if languageCode == "" {
		languageCode = constants.DefaultLanguageCode
	}

	types, err := l.service.GetMoreHoursTypes(ctx, req.GetBusinessId(), languageCode)
	if err != nil {
		return nil, err
	}
	return &listing_products_v1.GetMoreHoursTypesResponse{
		MoreHoursTypes: types,
	}, nil
}

// GetAttributeMetadata returns a list of attributes available for the listing profile
func (l *ListingProfileServer) GetAttributeMetadata(ctx context.Context, req *listing_products_v1.GetAttributeMetadataRequest) (*listing_products_v1.GetAttributeMetadataResponse, error) {
	err := validation.NewValidator().Rule(rules.StringNotEmpty(req.GetBusinessId(), "business_id is required")).Validate()
	if err != nil {
		return nil, err
	}

	attributes, err := l.service.GetAttributeMetadata(ctx, req.GetBusinessId(), req.GetVendorId(), req.GetLanguageCode())
	if err != nil {
		return nil, err
	}

	return &listing_products_v1.GetAttributeMetadataResponse{
		AttributeMedata: attributes,
	}, nil
}

func NewListingProfileServer(
	s *listingprofileservice.Service,
	as authservice.Interface,
	lp *ListingProfileAdapter,
	uberallhttpclienter uberallhttpclient.HttpClienter,
	accountGroup accountgroup.Interface,
	bpfsService businessprofilefieldstatusservice.Service,
	accountsService accounts.Interface,
	seoDataService seodataservice.Service,
	seoSettingsService seosettingsservice.Service) *ListingProfileServer {
	return &ListingProfileServer{
		service:               s,
		authService:           as,
		listingProfileAdapter: lp,
		uberallHttpClient:     uberallhttpclienter,
		accountGroup:          accountGroup,
		bpfsService:           bpfsService,
		accountsService:       accountsService,
		seoDataService:        seoDataService,
		seoSettingsService:    seoSettingsService,
	}
}

func (l *ListingProfileServer) Create(ctx context.Context, request *listing_products_v1.CreateListingProfileRequest) (*listing_products_v1.CreateListingProfileResponse, error) {
	externalIDs := getExternalIDs(request.GetUpdateOperations())
	err := validation.NewValidator().Rule(
		validation.ValueNotNil(externalIDs, verrors.InvalidArgument, "ExternalIDs are required on create"),
	).Validate()
	if err != nil {
		return nil, err
	}

	partnerID := externalIDs.GetPartnerId()
	err = l.authService.AccessListingProfileResources(ctx, []*listingproductsresources.ListingProfile{
		{
			PartnerID:  partnerID,
			BusinessID: request.GetBusinessLocationId(),
		},
	}, policy.ActionWrite)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	updateOps, agUpdateOps, err := l.listingProfileAdapter.UpdateOperationsToMutations(ctx, constants.DefaultLanguageCode, request.GetBusinessLocationId(), request.GetNap().GetCountry(), request.GetUpdateOperations())
	if err != nil {
		return nil, verrors.WrapError(err, "Error converting mutations from proto")
	}

	businessID := request.GetBusinessLocationId()
	if request.GetBusinessLocationId() == "" {
		nap, mutations, createOps, err := l.listingProfileAdapter.updateOperationsToCreateParams(ctx, request.GetNap(), request.GetUpdateOperations(), request.GetCreateOperations())
		if err != nil {
			return nil, err
		}
		businessID, err = l.accountGroup.CreateWithOperations(ctx, "", nap, mutations, createOps)
		if err != nil {
			return nil, err
		}
	} else {
		if len(agUpdateOps) > 0 {
			err = l.accountGroup.UpdateConcurrent(ctx, request.GetBusinessLocationId(), time.Time{}, agUpdateOps...)
			if err != nil {
				logging.Errorf(ctx, "Error updating account group")
				return nil, err
			}
		}
	}

	err = l.service.Upsert(ctx, businessID, time.Time{}, updateOps...)
	if err != nil {
		logging.Errorf(ctx, "Error upserting listing profile: %s", err.Error())
		return nil, verrors.WrapError(err, "Error creating account group")
	}

	return &listing_products_v1.CreateListingProfileResponse{BusinessId: businessID}, nil
}

func (l *ListingProfileServer) GetMulti(ctx context.Context, request *listing_products_v1.GetMultiListingProfileRequest) (*listing_products_v1.GetMultiListingProfileResponse, error) {
	if len(request.GetBusinessIds()) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "At least one ID must be provided for listing_profile_ids.")
	}
	for _, accountGroupID := range request.GetBusinessIds() {
		if accountGroupID == "" {
			return nil, status.Errorf(codes.InvalidArgument, "Empty listing_profile_id provided.")
		}
	}
	readFilter := request.GetReadFilter()
	includeDeleted := false
	if readFilter != nil {
		includeDeleted = readFilter.IncludeDeleted
	}
	profiles, err := l.service.GetMulti(ctx, request.GetBusinessIds(), includeDeleted)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	resources := make([]*listingproductsresources.ListingProfile, 0, len(profiles))
	for _, profile := range profiles {
		if profile == nil {
			continue
		}
		resource := &listingproductsresources.ListingProfile{
			PartnerID:  profile.PartnerID,
			BusinessID: profile.BusinessID,
		}
		resources = append(resources, resource)
	}

	if len(resources) > 0 {
		err = l.authService.AccessListingProfileResources(ctx, resources, policy.ActionRead)
		if err != nil {
			return nil, verrors.ToGrpcError(err)
		}
	}

	var profileContainer []*listing_products_v1.GetMultiListingProfileResponse_ListingProfileContainer
	for _, profile := range profiles {
		if profile == nil {
			profileContainer = append(profileContainer, &listing_products_v1.GetMultiListingProfileResponse_ListingProfileContainer{})
			continue
		}
		profileProto, err := listingProfileToProto(ctx, profile, request.GetProjectionFilter())
		if err != nil {
			return nil, verrors.ToGrpcError(err)
		}
		profileContainer = append(profileContainer, &listing_products_v1.GetMultiListingProfileResponse_ListingProfileContainer{
			ListingProfile: profileProto,
		})
	}
	return &listing_products_v1.GetMultiListingProfileResponse{
		ListingProfiles: profileContainer,
	}, nil
}

func (l *ListingProfileServer) Update(ctx context.Context, request *listing_products_v1.UpdateListingProfileRequest) (*empty.Empty, error) {
	ag, err := l.accountGroup.Get(ctx, request.GetBusinessId(), accountgroup.IncludeAccountGroupExternalIdentifiers(), accountgroup.IncludeNAPData())
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	err = l.authService.AccessListingProfileResources(ctx, []*listingproductsresources.ListingProfile{
		{
			PartnerID:  ag.ExternalIdentifiers.PartnerID,
			BusinessID: ag.AccountGroupID,
		},
	}, policy.ActionWrite)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	var ifUnmodifiedSince time.Time
	if request.GetIfUnmodifiedSince() != nil {
		ifUnmodifiedSince, err = ptypes.Timestamp(request.GetIfUnmodifiedSince())
		if err != nil {
			return nil, err
		}
	}

	// Needed for the ICO field validation
	country := ag.NAPData.Country

	// Check if the update operation contains a country update, if so, use that for ICO field validation
	for _, op := range request.GetUpdateOperations() {
		switch c := op.Operation.(type) {
		case *listing_products_v1.UpdateOperation_Nap:
			country = c.Nap.Country

		case *listing_products_v1.UpdateOperation_ExternalIdentifiers:
			if c.ExternalIdentifiers != nil {
				_ = trackUpdatingTaxonomy(ctx, c.ExternalIdentifiers.TaxIds, ag.ExternalIdentifiers.TaxIDs,
					c.ExternalIdentifiers.VCategoryIds, ag.ExternalIdentifiers.VCategoryIDs, request.GetBusinessId(), ag.ExternalIdentifiers.PartnerID)
			}
		}
	}

	ms, agMutations, err := l.listingProfileAdapter.UpdateOperationsToMutations(ctx, constants.DefaultLanguageCode, request.GetBusinessId(), country, request.GetUpdateOperations())
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	err = l.accountGroup.UpdateConcurrent(ctx, request.GetBusinessId(), ifUnmodifiedSince, agMutations...)
	if err != nil {
		logging.Errorf(ctx, "Error updating account group: %s", err.Error())
		return nil, err
	}

	err = l.service.Upsert(ctx, request.GetBusinessId(), ifUnmodifiedSince, ms...)
	if err != nil {
		return nil, err
	}

	return &empty.Empty{}, nil
}

func (l *ListingProfileServer) Delete(ctx context.Context, request *listing_products_v1.DeleteListingProfileRequest) (*empty.Empty, error) {
	profile, err := l.service.Get(ctx, request.GetBusinessId(), false)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	err = l.authService.AccessListingProfileResources(ctx, []*listingproductsresources.ListingProfile{
		{
			PartnerID:  profile.PartnerID,
			BusinessID: profile.BusinessID,
		},
	}, policy.ActionDelete)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	err = l.service.Delete(ctx, request.GetBusinessId())
	if err != nil {
		return nil, err
	}
	return &empty.Empty{}, nil
}

func (l *ListingProfileServer) LegacyAPICreate(ctx context.Context, request *listing_products_v1.LegacyAPICreateRequest) (*empty.Empty, error) {
	var body listingprofilelegacy.AccountGroup
	err := json.Unmarshal([]byte(request.GetArgs()), &body)
	if err != nil {
		logging.Errorf(ctx, "LegacyAPICreate json unmarshal error: %s: %s", err.Error(), request.GetArgs())
		statsd.Incr(constants.LegacyAccountCreateErrorMetric, []string{"error:json_unmarshal"}, 1)
		return nil, err
	}

	resp, err := l.GetMulti(ctx, &listing_products_v1.GetMultiListingProfileRequest{
		BusinessIds:      []string{request.GetBusinessId()},
		ProjectionFilter: &listing_products_v1.ProjectionFilter{RichData: true},
	})
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		logging.Errorf(ctx, "LegacyAPICreate error fetching existing Listing Profile: %s: %s", err.Error(), request.GetArgs())
		statsd.Incr(constants.LegacyAccountCreateErrorMetric, []string{"error:get_multi"}, 1)
		return nil, err
	}
	var listingProfile *listing_products_v1.ListingProfile
	if len(resp.GetListingProfiles()) > 0 {
		listingProfile = resp.GetListingProfiles()[0].GetListingProfile()
	}

	keywordLimit := l.listingProfileAdapter.getKeywordLimit(ctx, request.GetBusinessId())
	operations := body.ToUpdateOperations(listingProfile, keywordLimit)

	_, err = l.Update(ctx, &listing_products_v1.UpdateListingProfileRequest{
		BusinessId:       request.GetBusinessId(),
		UpdateOperations: operations,
	})
	if err != nil {
		logging.Errorf(ctx, "LegacyAPICreate error upserting Listing Profile: %s: %s", err.Error(), request.GetArgs())
		statsd.Incr(constants.LegacyAccountCreateErrorMetric, []string{"error:create"}, 1)
		return nil, err
	}

	statsd.Incr(constants.LegacyAccountCreateMetric, []string{}, 1)
	return &empty.Empty{}, nil
}

func (l *ListingProfileServer) LegacyAPIUpdate(ctx context.Context, request *listing_products_v1.LegacyAPIUpdateRequest) (*empty.Empty, error) {
	var body listingprofilelegacy.AccountGroup
	err := json.Unmarshal([]byte(request.GetArgs()), &body)
	if err != nil {
		logging.Errorf(ctx, "Error unmarshalling LegacyAPIUpdate body: %s: %s", err.Error(), request.GetArgs())
		statsd.Incr(constants.LegacyAccountUpdateErrorMetric, []string{"error:json_unmarshal"}, 1)
		return nil, err
	}

	// There are two API fields VBC might be sending for account group ID.  Check for both.
	accountGroupID := body.AccountID
	if accountGroupID == "" {
		accountGroupID = body.AccountGroupID
	}

	resp, err := l.GetMulti(ctx, &listing_products_v1.GetMultiListingProfileRequest{
		BusinessIds:      []string{accountGroupID},
		ProjectionFilter: &listing_products_v1.ProjectionFilter{RichData: true},
	})
	if err != nil {
		logging.Errorf(ctx, "LegacyAPIUpdate error fetching existing Listing Profile: %s: %s", err.Error(), request.GetArgs())
		statsd.Incr(constants.LegacyAccountUpdateErrorMetric, []string{"error:get_multi"}, 1)
		return nil, err
	}
	var listingProfile *listing_products_v1.ListingProfile
	if len(resp.GetListingProfiles()) > 0 && resp.GetListingProfiles()[0].GetListingProfile() != nil {
		listingProfile = resp.GetListingProfiles()[0].GetListingProfile()
	} else {
		logging.Errorf(ctx, "LegacyAPIUpdate existing Listing Profile not found: %s:", request.GetArgs())
		statsd.Incr(constants.LegacyAccountUpdateErrorMetric, []string{"error:get_multi"}, 1)
		return nil, verrors.New(verrors.NotFound, "Listing Profile not found")
	}

	if body.TaxonomyID != nil && len(body.TaxonomyID) > 0 {
		logging.Infof(ctx, "LegacyAPIUpdate update account group with legacy taxonomyID for %s", body.AccountGroupID)
		_ = statsd.Incr("update_v_taxonomy", []string{"case:legacy"}, 1)
	}

	keywordLimit := l.listingProfileAdapter.getKeywordLimit(ctx, listingProfile.BusinessId)
	operations := body.ToUpdateOperations(listingProfile, keywordLimit)
	_, err = l.Update(ctx, &listing_products_v1.UpdateListingProfileRequest{
		BusinessId:       accountGroupID,
		UpdateOperations: operations,
	})
	if err != nil {
		logging.Errorf(ctx, "LegacyAPIUpdate error updating Listing Profile: %s: %s", err.Error(), request.GetArgs())
		statsd.Incr(constants.LegacyAccountUpdateErrorMetric, []string{"error:update"}, 1)
		return nil, err
	}

	statsd.Incr(constants.LegacyAccountUpdateMetric, []string{}, 1)
	return &empty.Empty{}, nil
}

func (l *ListingProfileServer) GetBusinessProfileFieldStatus(ctx context.Context, request *listing_products_v1.GetBusinessProfileFieldStatusRequest) (*listing_products_v1.GetBusinessProfileFieldStatusResponse, error) {
	err := validation.NewValidator().
		Rule(rules.StringNotEmpty(request.GetBusinessId(), "business_id is required")).
		Validate()
	if err != nil {
		return nil, err
	}
	businessProfileFieldStatus, err := l.bpfsService.Get(ctx, request.GetBusinessId())
	if err != nil {
		return nil, err
	}
	return &listing_products_v1.GetBusinessProfileFieldStatusResponse{
		BusinessId:              businessProfileFieldStatus.BusinessID,
		CompanyName:             fieldStatusToProto(businessProfileFieldStatus.CompanyName),
		Address:                 fieldStatusToProto(businessProfileFieldStatus.Address),
		Address2:                fieldStatusToProto(businessProfileFieldStatus.Address2),
		City:                    fieldStatusToProto(businessProfileFieldStatus.City),
		State:                   fieldStatusToProto(businessProfileFieldStatus.State),
		Zip:                     fieldStatusToProto(businessProfileFieldStatus.Zip),
		Country:                 fieldStatusToProto(businessProfileFieldStatus.Country),
		WorkNumber:              fieldStatusToProto(businessProfileFieldStatus.WorkNumber),
		CallTrackingNumber:      fieldStatusToProto(businessProfileFieldStatus.CallTrackingNumber),
		Website:                 fieldStatusToProto(businessProfileFieldStatus.Website),
		Location:                fieldStatusToProto(businessProfileFieldStatus.Location),
		SalesPersonId:           fieldStatusToProto(businessProfileFieldStatus.SalesPersonID),
		AdditionalSalesPersonId: fieldStatusToProto(businessProfileFieldStatus.AdditionalSalesPersonIDs),
		VcategoryIds:            fieldStatusToProto(businessProfileFieldStatus.VCategoryIDs),
		CustomerIdentifier:      fieldStatusToProto(businessProfileFieldStatus.CustomerIdentifier),
		LifecycleStage:          fieldStatusToProto(businessProfileFieldStatus.LifecycleStage),
		Created:                 timestamppb.New(businessProfileFieldStatus.Created),
		Updated:                 timestamppb.New(businessProfileFieldStatus.Updated),
		Deleted:                 timestamppb.New(businessProfileFieldStatus.Deleted),
	}, nil
}

func (l *ListingProfileServer) GetMultiAccountGroup(ctx context.Context, request *listing_products_v1.GetMultiAccountGroupRequest) (*listing_products_v1.GetMultiAccountGroupResponse, error) {
	if len(request.GetBusinessIds()) == 0 {
		return nil, verrors.ToGrpcError(verrors.New(verrors.InvalidArgument, "At least one business_id must be provided."))
	}
	for _, businessID := range request.GetBusinessIds() {
		if businessID == "" {
			return nil, verrors.ToGrpcError(verrors.New(verrors.InvalidArgument, "Empty business_id provided."))
		}
	}

	languageCode := request.GetLanguageCode()
	if languageCode == "" {
		languageCode = constants.DefaultLanguageCode
	}

	readFilter := request.GetReadFilter()
	includeDeleted := false
	if readFilter != nil {
		includeDeleted = readFilter.IncludeDeleted
	}

	profiles, err := l.service.GetMulti(ctx, request.GetBusinessIds(), includeDeleted)
	if err != nil {
		logging.Errorf(ctx, "Error getting listing profiles: %s", err.Error())
		return nil, verrors.ToGrpcError(err)
	}

	agGetMultiOptions := []accountgroup.Option{
		accountgroup.IncludeNAPData(),
		accountgroup.IncludeAccountGroupExternalIdentifiers(),
		accountgroup.IncludeSocialUrls(),
		accountgroup.IncludeRichData(),
	}
	if includeDeleted {
		agGetMultiOptions = append(agGetMultiOptions, accountgroup.IncludeDeleted())
	}
	accountGroups, err := l.accountGroup.GetMulti(ctx, request.GetBusinessIds(), agGetMultiOptions...)
	if err != nil {
		logging.Errorf(ctx, "Error getting account groups: %s", err.Error())
		return nil, verrors.ToGrpcError(err)
	}

	resources := make([]*listingproductsresources.ListingProfile, 0, len(profiles))
	for _, profile := range profiles {
		if profile == nil {
			continue
		}
		resource := &listingproductsresources.ListingProfile{
			PartnerID:  profile.PartnerID,
			BusinessID: profile.BusinessID,
		}
		resources = append(resources, resource)
	}

	if len(resources) > 0 {
		err = l.authService.AccessListingProfileResources(ctx, resources, policy.ActionRead)
		if err != nil {
			return nil, verrors.ToGrpcError(err)
		}
	}

	type businessData struct {
		index              int
		businessID         string
		activationStatuses *listing_products_v1.ActivationStatusData
		seoAddons          *listing_products_v1.SEOAddonData
		seoSettings        *listing_products_v1.SEOSettingsData
	}

	businessDataChan := make(chan *businessData, len(request.GetBusinessIds()))
	g, egCtx := errgroup.WithContext(ctx)

	for i, businessID := range request.GetBusinessIds() {
		i, businessID := i, businessID
		g.Go(func() error {
			data := &businessData{
				index:      i,
				businessID: businessID,
			}
			activationStatuses, err := l.accountsService.ListAppsAndAddonsActivationStatusesForBusiness(egCtx, businessID)
			if err != nil {
				logging.Errorf(egCtx, "Error getting activation statuses for business %s: %s", businessID, err.Error())
			} else {
				var convertedStatuses []listing_products_v1.AppAndAddonActivationStatus
				for _, status := range activationStatuses.ActivationStatuses {
					convertedStatuses = append(convertedStatuses, listing_products_v1.AppAndAddonActivationStatus(status.Status))
				}
				data.activationStatuses = &listing_products_v1.ActivationStatusData{
					ActivationStatuses: convertedStatuses,
				}
			}
			seoAddons, err := l.seoDataService.GetActiveKeywordAddons(egCtx, businessID)
			if err != nil {
				logging.Errorf(egCtx, "Error getting SEO addons for business %s: %s", businessID, err.Error())
			} else {
				var seoAddonActivations []*listing_products_v1.SEOAddonActivation
				for _, addon := range seoAddons {
					seoAddonActivations = append(seoAddonActivations, &listing_products_v1.SEOAddonActivation{
						BusinessId: addon.BusinessID,
						AddonId:    addon.AddonID,
						AppId:      addon.AppID,
					})
				}
				data.seoAddons = &listing_products_v1.SEOAddonData{
					Addons: seoAddonActivations,
				}
			}
			seoSettings, err := l.seoSettingsService.Get(egCtx, businessID)
			if err != nil {
				logging.Errorf(egCtx, "Error getting SEO settings for business %s: %s", businessID, err.Error())
			} else {
				data.seoSettings = &listing_products_v1.SEOSettingsData{
					LocalSearchRadius:   seoSettings.LocalSearchRadius,
					FavoriteKeywords:    seoSettings.FavoriteKeywords,
					IsFullSearchEnabled: seoSettings.IsFullSearchEnabled,
				}
			}

			select {
			case businessDataChan <- data:
			case <-egCtx.Done():
				return egCtx.Err()
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, verrors.ToGrpcError(err)
	}
	close(businessDataChan)

	businessDataMap := make(map[int]*businessData)
	for data := range businessDataChan {
		businessDataMap[data.index] = data
	}
	var consolidatedData []*listing_products_v1.ConsolidatedDataContainer
	for i, businessID := range request.GetBusinessIds() {
		container := &listing_products_v1.ConsolidatedDataContainer{
			BusinessId: businessID,
		}

		if i < len(profiles) && profiles[i] != nil {
			profileProto, err := listingProfileToProto(ctx, profiles[i], request.GetProjectionFilter())
			if err != nil {
				logging.Errorf(ctx, "Error converting listing profile to proto: %s", err.Error())
				return nil, verrors.ToGrpcError(err)
			}
			container.ListingProfile = profileProto
		}

		if i < len(accountGroups) && accountGroups[i] != nil {
			ag := accountGroups[i]
			container.AccountGroup = &listing_products_v1.AccountGroupData{
				AccountGroupId: ag.AccountGroupID,
			}
			if ag.ExternalIdentifiers != nil {
				container.AccountGroup.ExternalIdentifiers = &listing_products_v1.ExternalIdentifiers{
					Origin:                   ag.ExternalIdentifiers.Origin,
					JobId:                    ag.ExternalIdentifiers.JobID,
					CustomerIdentifier:       ag.ExternalIdentifiers.CustomerIdentifier,
					Tags:                     ag.ExternalIdentifiers.Tags,
					ActionLists:              ag.ExternalIdentifiers.ActionLists,
					SocialProfileId:          ag.ExternalIdentifiers.SocialProfileID,
					PartnerId:                ag.ExternalIdentifiers.PartnerID,
					MarketId:                 ag.ExternalIdentifiers.MarketID,
					TaxIds:                   ag.ExternalIdentifiers.TaxIDs,
					SalesPersonId:            ag.ExternalIdentifiers.SalesPersonID,
					AdditionalSalesPersonIds: ag.ExternalIdentifiers.AdditionalSalesPersonIDs,
					VCategoryIds:             ag.ExternalIdentifiers.VCategoryIDs,
					UpdateOrigin:             ag.ExternalIdentifiers.UpdateOrigin,
				}
			}
			if ag.SocialURLs != nil {
				container.AccountGroup.SocialUrls = &listing_products_v1.SocialURLs{
					GoogleplusUrl: ag.SocialURLs.GoogleplusURL,
					LinkedinUrl:   ag.SocialURLs.LinkedinURL,
					FoursquareUrl: ag.SocialURLs.FoursquareURL,
					TwitterUrl:    ag.SocialURLs.TwitterURL,
					FacebookUrl:   ag.SocialURLs.FacebookURL,
					RssUrl:        ag.SocialURLs.RssURL,
					YoutubeUrl:    ag.SocialURLs.YoutubeURL,
					InstagramUrl:  ag.SocialURLs.InstagramURL,
					PinterestUrl:  ag.SocialURLs.PinterestURL,
				}
			}
			if ag.RichData != nil {
				container.AccountGroup.RichData = &listing_products_v1.RichData{
					TollFreeNumber:     ag.RichData.TollFreeNumber,
					Description:        ag.RichData.Description,
					ShortDescription:   ag.RichData.ShortDescription,
					ServicesOffered:    ag.RichData.ServicesOffered,
					BrandsCarried:      ag.RichData.BrandsCarried,
					Landmark:           ag.RichData.Landmark,
					InferredAttributes: ag.RichData.InferredAttributes,
				}
			}
			if ag.NAPData != nil {
				container.AccountGroup.NapData = &listing_products_v1.Location{
					CompanyName:         ag.NAPData.CompanyName,
					Address:             ag.NAPData.Address,
					Address2:            ag.NAPData.Address2,
					City:                ag.NAPData.City,
					State:               ag.NAPData.State,
					Zip:                 ag.NAPData.Zip,
					Country:             ag.NAPData.Country,
					Website:             ag.NAPData.Website,
					WorkNumber:          ag.NAPData.WorkNumber,
					CallTrackingNumber:  ag.NAPData.CallTrackingNumber,
					Timezone:            ag.NAPData.Timezone,
					ServiceAreaBusiness: ag.NAPData.ServiceAreaBusiness,
				}
			}
		}

		if data, exists := businessDataMap[i]; exists {
			container.ActivationStatus = data.activationStatuses
			container.SeoAddons = data.seoAddons
			container.SeoSettings = data.seoSettings
		}

		consolidatedData = append(consolidatedData, container)
	}

	return &listing_products_v1.GetMultiAccountGroupResponse{
		ConsolidatedData: consolidatedData,
	}, nil
}
func getExternalIDs(operations []*listing_products_v1.UpdateOperation) *listing_products_v1.ExternalIdentifiers {
	var externalIDs *listing_products_v1.ExternalIdentifiers
	for _, op := range operations {
		externalIDs = op.GetExternalIdentifiers()
		if externalIDs != nil {
			break
		}
	}
	return externalIDs
}

func getAgUpdateOperations(updateOperations []*listing_products_v1.UpdateOperation) []*listing_products_v1.UpdateOperation {
	var updateOps []*listing_products_v1.UpdateOperation
	for _, op := range updateOperations {
		switch op.GetOperation().(type) {
		case *listing_products_v1.UpdateOperation_Nap:
			updateOps = append(updateOps, op)
		case *listing_products_v1.UpdateOperation_RichData:
			updateOps = append(updateOps, op)
		case *listing_products_v1.UpdateOperation_SocialUrls:
			updateOps = append(updateOps, op)
		default:
			continue
		}
	}
	return updateOperations
}

func trackUpdatingTaxonomy(ctx context.Context, updatedTaxIDs, currentTaxIDs, updatedVCategoryIDs, currentVCategoryIDs []string,
	businessID, partnerID string) string {
	if len(updatedTaxIDs) == 0 {
		return ""
	}

	tag := "api_update_taxonomy"
	if utils.AreTaxonomiesEqual(updatedTaxIDs, currentTaxIDs) {
		tag = "api_update_taxonomy_no_change"
	} else {
		logging.Infof(ctx, "Update legacy taxonomy for the business ID %s on partner %s", businessID, partnerID)
		if len(updatedVCategoryIDs) > 0 && !utils.AreTaxonomiesEqual(updatedVCategoryIDs, currentVCategoryIDs) {
			tag = "api_update_taxonomy_v_category"
		}
	}

	_ = statsd.Incr("update_v_taxonomy", []string{fmt.Sprintf("case:%s", tag)}, 1)
	return tag
}
