package api

import (
	"context"
	"fmt"
	"testing"
	"time"

	accounts "github.com/vendasta/accounts/sdks/go/v1"
	accounts_v1 "github.com/vendasta/generated-protos-go/accounts/v1"
	businessprofilefieldstatusservice "github.com/vendasta/listing-products/internal/businessprofilefieldstatus/service"
	uberallhttpclient "github.com/vendasta/listing-products/internal/syndication/uberall/httpclient"

	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	"github.com/vendasta/gosdks/verrors"
	"github.com/vendasta/listing-products/internal/geo"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	gbp "google.golang.org/api/mybusinessbusinessinformation/v1"

	accountsV2 "github.com/vendasta/accounts/sdks/go/v2"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes"
	"github.com/stretchr/testify/assert"
	aasdk "github.com/vendasta/AA/sdks/go/v1"
	cssdk "github.com/vendasta/CS/sdks/go/v1"
	nap "github.com/vendasta/NAP/sdks/go/v1"
	accountsProtos "github.com/vendasta/generated-protos-go/accounts/v2"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	vendastatypes "github.com/vendasta/generated-protos-go/vendasta_types"
	addonattributesservice "github.com/vendasta/listing-products/internal/addonattributes/service"
	"github.com/vendasta/listing-products/internal/authservice"
	"github.com/vendasta/listing-products/internal/common/accountgroupwrapper"
	"github.com/vendasta/listing-products/internal/google/businessprofile"
	"github.com/vendasta/listing-products/internal/listingprofile"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofilerepository "github.com/vendasta/listing-products/internal/listingprofile/repository"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	seodatasettingsmodel "github.com/vendasta/listing-products/internal/seosettings/model"
	"github.com/vendasta/listing-products/internal/syndication/submissions/apiworkflow"
	listingsyncpro "github.com/vendasta/listing-sync-pro/sdks/go"
	partner "github.com/vendasta/partner/sdks/go/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func Test_GetMulti_e2e(t *testing.T) {
	type testCase struct {
		name                       string
		request                    *listing_products_v1.GetMultiListingProfileRequest
		expectedProfile            *listingprofilemodel.ListingProfile
		expectedAccountGroup       *accountgroup.AccountGroup
		expectedAttributesMetadata *gbp.ListAttributeMetadataResponse
		expectedResponse           *listing_products_v1.GetMultiListingProfileResponse
		expectedErr                error
	}
	cases := []*testCase{
		{
			name: "Test more hours types and attributes are not fetched when not included in projection filter",
			request: &listing_products_v1.GetMultiListingProfileRequest{
				BusinessIds: []string{"AG-HDV4JKPXFR"},
				ProjectionFilter: &listing_products_v1.ProjectionFilter{
					NapData: true,
				},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-HDV4JKPXFR",
				PartnerID:   "ABC",
				CompanyName: "Company Name",
				Address:     "2015  Upton Avenue",
				Address2:    "",
				City:        "Bangor",
				State:       "ME",
				Zip:         "04401",
				Deleted:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				Created:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				Updated:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				RichData:    &listingprofilemodel.RichData{},
				Attributes:  "[{}]",
			},
			expectedResponse: &listing_products_v1.GetMultiListingProfileResponse{
				ListingProfiles: []*listing_products_v1.GetMultiListingProfileResponse_ListingProfileContainer{
					{
						ListingProfile: &listing_products_v1.ListingProfile{
							BusinessId: "AG-HDV4JKPXFR",
							Deleted:    nil,
							Created:    &timestamppb.Timestamp{Seconds: -***********, Nanos: 0},
							Updated:    &timestamppb.Timestamp{Seconds: -***********, Nanos: 0}, Version: 0,
							ExternalIdentifiers: nil, SocialUrls: nil, HoursOfOperation: nil, RichData: nil,
							NapData: &listing_products_v1.Location{
								CompanyName: "Company Name",
								Address:     "2015  Upton Avenue",
								City:        "Bangor",
								State:       "ME",
								Zip:         "04401",
							},
						},
					},
				},
			},
		},
		{
			name: "Test if ico field is returned when country is CZ",
			request: &listing_products_v1.GetMultiListingProfileRequest{
				BusinessIds: []string{"AG-HDV4JKPXFR"},
				ProjectionFilter: &listing_products_v1.ProjectionFilter{
					NapData:  true,
					RichData: true,
				},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-HDV4JKPXFR",
				PartnerID:   "ABC",
				CompanyName: "Company Name",
				Address:     "2015  Upton Avenue",
				Address2:    "",
				City:        "Bangor",
				State:       "ME",
				Zip:         "04401",
				Country:     "CZ",
				Deleted:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				Created:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				Updated:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				RichData: &listingprofilemodel.RichData{
					ConditionalFields: []*listingprofilemodel.ConditionalField{
						{
							ID:    "ico",
							Value: "1231231",
						},
					},
					PaymentMethods: []listingprofilemodel.RichDataPaymentMethods{
						1, 2, 3,
					},
				},
				Attributes: "[{}]",
			},
			expectedResponse: &listing_products_v1.GetMultiListingProfileResponse{
				ListingProfiles: []*listing_products_v1.GetMultiListingProfileResponse_ListingProfileContainer{
					{
						ListingProfile: &listing_products_v1.ListingProfile{
							BusinessId: "AG-HDV4JKPXFR",
							Deleted:    nil,
							Created:    &timestamppb.Timestamp{Seconds: -***********, Nanos: 0},
							Updated:    &timestamppb.Timestamp{Seconds: -***********, Nanos: 0}, Version: 0,
							ExternalIdentifiers: nil, SocialUrls: nil, HoursOfOperation: nil,
							NapData: &listing_products_v1.Location{
								CompanyName: "Company Name",
								Address:     "2015  Upton Avenue",
								City:        "Bangor",
								State:       "ME",
								Zip:         "04401",
								Country:     "CZ",
							},
							RichData: &listing_products_v1.RichData{
								ConditionalFields: []*listing_products_v1.ConditionalField{
									{
										Id:    "ico",
										Value: "1231231",
									},
								},
								PaymentMethods: []listing_products_v1.RichData_PaymentMethods{
									listing_products_v1.RichData_ANDROID_PAY,
									listing_products_v1.RichData_APPLE_PAY,
									listing_products_v1.RichData_CASH,
								},
							},
						},
					},
				},
			},
		},
		{
			name: "Test if ico field is not returned when country is not CZ",
			request: &listing_products_v1.GetMultiListingProfileRequest{
				BusinessIds: []string{"AG-HDV4JKPXFR"},
				ProjectionFilter: &listing_products_v1.ProjectionFilter{
					NapData:  true,
					RichData: true,
				},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID:  "AG-HDV4JKPXFR",
				PartnerID:   "ABC",
				CompanyName: "Company Name",
				Address:     "2015  Upton Avenue",
				Address2:    "",
				City:        "Bangor",
				State:       "ME",
				Zip:         "04401",
				Country:     "US",
				Deleted:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				Created:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				Updated:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
				RichData: &listingprofilemodel.RichData{
					ConditionalFields: []*listingprofilemodel.ConditionalField{
						{
							ID:    "ico",
							Value: "1231231",
						},
					},
					PaymentMethods: []listingprofilemodel.RichDataPaymentMethods{
						1, 2, 3,
					},
				},
				Attributes: "[{}]",
			},
			expectedResponse: &listing_products_v1.GetMultiListingProfileResponse{
				ListingProfiles: []*listing_products_v1.GetMultiListingProfileResponse_ListingProfileContainer{
					{
						ListingProfile: &listing_products_v1.ListingProfile{
							BusinessId: "AG-HDV4JKPXFR",
							Deleted:    nil,
							Created:    &timestamppb.Timestamp{Seconds: -***********, Nanos: 0},
							Updated:    &timestamppb.Timestamp{Seconds: -***********, Nanos: 0}, Version: 0,
							ExternalIdentifiers: nil, SocialUrls: nil, HoursOfOperation: nil,
							NapData: &listing_products_v1.Location{
								CompanyName: "Company Name",
								Address:     "2015  Upton Avenue",
								City:        "Bangor",
								State:       "ME",
								Zip:         "04401",
								Country:     "US",
							},
							RichData: &listing_products_v1.RichData{
								PaymentMethods: []listing_products_v1.RichData_PaymentMethods{
									listing_products_v1.RichData_ANDROID_PAY,
									listing_products_v1.RichData_APPLE_PAY,
									listing_products_v1.RichData_CASH,
								},
							},
						},
					},
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			mockListingProfileRepo := listingprofilerepository.NewMockRepository(ctrl)

			mockListingProfileRepo.EXPECT().GetMulti(gomock.Any(), c.request.GetBusinessIds(), false).Return([]*listingprofilemodel.ListingProfile{c.expectedProfile}, nil)

			mockAG := accountgroupwrapper.NewMockServer(ctrl)
			mockAG.EXPECT().GetMulti(gomock.Any(), c.request.GetBusinessIds(), gomock.Any()).Return([]*accountgroup.AccountGroup{c.expectedAccountGroup}, nil)

			mockBusinessProfile := businessprofile.NewMockServicer(ctrl)

			if c.expectedAttributesMetadata != nil {
				mockBusinessProfile.EXPECT().FetchGoogleAttributesMetadata(gomock.Any(), c.expectedProfile.BusinessID, gomock.Any()).Return(c.expectedAttributesMetadata, nil).AnyTimes()
			}

			mockTurboLister := apiworkflow.NewMockTurboListerAPISubmission(ctrl)
			mockLSP := listingsyncpro.NewMockListingSyncProServiceClientInterface(ctrl)
			mockAddonService := addonattributesservice.NewMockService(ctrl)
			mockYextClient := NewMockYextInterface(ctrl)
			mockNAP := nap.NewMockInterface(ctrl)
			mockPartner := &partner.MockInterface{}
			mockAA := &aasdk.MockPartnerClientInterface{}
			mockTax := cssdk.NewMockTaxonomyClientInterface(ctrl)
			mockGeo := geo.NewMockClient(52.1, -120.2, "EST", nil, nil)

			mockAuth := authservice.NewMockInterface(ctrl)
			mockAuth.EXPECT().AccessListingProfileResources(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			externalValidatorServices := listingprofile.NewExternalValidatorServices(mockNAP, mockPartner, mockAA, mockTax, mockGeo)

			listingProfileService := listingprofileservice.New(mockListingProfileRepo, mockBusinessProfile, mockAG,
				mockTurboLister, mockLSP, mockAddonService, mockYextClient, nil, nil, nil)
			listingProfileAdapterService := &ListingProfileAdapter{
				service: listingProfileService,
				evs:     externalValidatorServices,
			}
			mockSeoDataService := seodataservice.NewMockService(ctrl)
			mockSeoSettingsService := seosettingsservice.NewMockService(ctrl)

			server := NewListingProfileServer(listingProfileService, mockAuth, listingProfileAdapterService, nil, mockAG, nil, nil, mockSeoDataService, mockSeoSettingsService)

			resp, err := server.GetMulti(ctx, c.request)

			assert.Equal(t, c.expectedResponse, resp)
			assert.Equal(t, c.expectedErr, err)
		})
	}
}

func Test_Update_e2e(t *testing.T) {

	type testCase struct {
		name                 string
		request              *listing_products_v1.UpdateListingProfileRequest
		expectedProfile      *listingprofilemodel.ListingProfile
		accountGroupMutators []accountgroup.MutateOption
		expectedAccountGroup *accountgroup.AccountGroup
		expectedErr          error
		expectedRepoErr      error
	}
	cases := []*testCase{
		{
			name: "Test it updates rich data",
			request: &listing_products_v1.UpdateListingProfileRequest{
				BusinessId: "AG-HDV4JKPXFR",
				UpdateOperations: []*listing_products_v1.UpdateOperation{
					{
						Operation: &listing_products_v1.UpdateOperation_RichData{
							RichData: &listing_products_v1.RichData{
								TollFreeNumber: "1231231",
							},
						},
						FieldMask: &vendastatypes.FieldMask{
							Paths: []string{"tollFreeNumber"},
						},
					},
				},
				IfUnmodifiedSince: &timestamppb.Timestamp{Seconds: 0, Nanos: 0},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
				ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
					PartnerID: "ABC",
				},
				NAPData: &accountgroup.NAPData{
					Country: "CA",
				},
				LegacyProductDetails: &accountgroup.LegacyProductDetails{},
			},
			accountGroupMutators: []accountgroup.MutateOption{
				&accountgroup.LegacyProductDetails{
					FaxNumber:  "",
					CellNumber: "",
					Email:      "",
					FieldMask:  []string{"cellNumber", "faxNumber", "email"},
				},
				&accountgroup.RichData{
					TollFreeNumber: "1231231",
					Landmark:       "",
					FieldMask:      []string{"landmark", "inferredAttributes", "tollFreeNumber"},
				},
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-HDV4JKPXFR",
				RichData: &listingprofilemodel.RichData{
					TollFreeNumber: "1231231",
				},
			},
		},
		{
			name: "Should not return an error when doing an ico field update",
			request: &listing_products_v1.UpdateListingProfileRequest{
				BusinessId: "AG-HDV4JKPXFR",
				UpdateOperations: []*listing_products_v1.UpdateOperation{
					{
						Operation: &listing_products_v1.UpdateOperation_RichData{
							RichData: &listing_products_v1.RichData{
								ConditionalFields: []*listing_products_v1.ConditionalField{
									{
										Id:    "ico",
										Value: "1231231",
									},
								},
							},
						},
						FieldMask: &vendastatypes.FieldMask{
							Paths: []string{"conditionalFields"},
						},
					},
				},
				IfUnmodifiedSince: &timestamppb.Timestamp{Seconds: 0, Nanos: 0},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
				ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
					PartnerID: "ABC",
				},
				NAPData: &accountgroup.NAPData{
					Country: "CZ",
				},
				LegacyProductDetails: &accountgroup.LegacyProductDetails{},
			},
			accountGroupMutators: []accountgroup.MutateOption{
				&accountgroup.LegacyProductDetails{
					FaxNumber:  "",
					CellNumber: "",
					Email:      "",
					FieldMask:  []string{"cellNumber", "faxNumber", "email"},
				},
				&accountgroup.RichData{
					TollFreeNumber: "",
					Landmark:       "",
					FieldMask:      []string{"landmark", "inferredAttributes", "tollFreeNumber"},
				},
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-HDV4JKPXFR",
				RichData: &listingprofilemodel.RichData{
					ConditionalFields: []*listingprofilemodel.ConditionalField{
						{
							ID:    "ico",
							Value: "1231231",
						},
					},
				},
			},
		},
		{
			name: "Should return an error if trying to update the ICO field and country is not CZ",
			request: &listing_products_v1.UpdateListingProfileRequest{
				BusinessId: "AG-HDV4JKPXFR",
				UpdateOperations: []*listing_products_v1.UpdateOperation{
					{
						Operation: &listing_products_v1.UpdateOperation_RichData{
							RichData: &listing_products_v1.RichData{
								ConditionalFields: []*listing_products_v1.ConditionalField{
									{
										Id:    "ico",
										Value: "1231231",
									},
								},
							},
						},
						FieldMask: &vendastatypes.FieldMask{
							Paths: []string{"conditionalFields"},
						},
					},
				},
				IfUnmodifiedSince: &timestamppb.Timestamp{Seconds: 0, Nanos: 0},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
				ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
					PartnerID: "ABC",
				},
				NAPData: &accountgroup.NAPData{
					Country: "US",
				},
				LegacyProductDetails: &accountgroup.LegacyProductDetails{},
			},
			accountGroupMutators: []accountgroup.MutateOption{
				&accountgroup.LegacyProductDetails{
					FaxNumber:  "",
					CellNumber: "",
					Email:      "",
					FieldMask:  []string{"cellNumber", "faxNumber", "email"},
				},
				&accountgroup.RichData{
					TollFreeNumber: "",
					Landmark:       "",
					FieldMask:      []string{"landmark", "inferredAttributes", "tollFreeNumber"},
				},
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-HDV4JKPXFR",
				RichData: &listingprofilemodel.RichData{
					ConditionalFields: []*listingprofilemodel.ConditionalField{
						{
							ID:    "ico",
							Value: "1231231",
						},
					},
				},
			},
			expectedErr:     verrors.New(verrors.InvalidArgument, "ico cannot be set for this business"),
			expectedRepoErr: verrors.New(verrors.InvalidArgument, "ico cannot be set for this business"),
		},
		{
			name: "Should return an error if trying to update the country to not CZ",
			request: &listing_products_v1.UpdateListingProfileRequest{
				BusinessId: "AG-HDV4JKPXFR",
				UpdateOperations: []*listing_products_v1.UpdateOperation{
					{
						Operation: &listing_products_v1.UpdateOperation_RichData{
							RichData: &listing_products_v1.RichData{
								ConditionalFields: []*listing_products_v1.ConditionalField{
									{
										Id:    "ico",
										Value: "1231231",
									},
								},
							},
						},
						FieldMask: &vendastatypes.FieldMask{
							Paths: []string{"conditionalFields"},
						},
					},
					{
						Operation: &listing_products_v1.UpdateOperation_Nap{
							Nap: &listing_products_v1.Location{
								Country: "US",
							},
						},
						FieldMask: &vendastatypes.FieldMask{
							Paths: []string{"country"},
						},
					},
				},
				IfUnmodifiedSince: &timestamppb.Timestamp{Seconds: 0, Nanos: 0},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
				ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
					PartnerID: "ABC",
				},
				NAPData: &accountgroup.NAPData{
					Country: "CZ",
				},
				LegacyProductDetails: &accountgroup.LegacyProductDetails{},
			},
			accountGroupMutators: []accountgroup.MutateOption{
				&accountgroup.LegacyProductDetails{
					FaxNumber:  "",
					CellNumber: "",
					Email:      "",
					FieldMask:  []string{"cellNumber", "faxNumber", "email"},
				},
				&accountgroup.RichData{
					TollFreeNumber: "",
					Landmark:       "",
					FieldMask:      []string{"landmark", "inferredAttributes", "tollFreeNumber"},
				},
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-HDV4JKPXFR",
				RichData: &listingprofilemodel.RichData{
					ConditionalFields: []*listingprofilemodel.ConditionalField{
						{
							ID:    "ico",
							Value: "1231231",
						},
					},
				},
			},
			expectedErr:     verrors.New(verrors.InvalidArgument, "ico cannot be set for this business"),
			expectedRepoErr: verrors.New(verrors.InvalidArgument, "ico cannot be set for this business"),
		},
		{
			name: "Should return an error if trying to update attributes with invalid values",
			request: &listing_products_v1.UpdateListingProfileRequest{
				BusinessId: "AG-HDV4JKPXFR",
				UpdateOperations: []*listing_products_v1.UpdateOperation{
					{
						Operation: &listing_products_v1.UpdateOperation_BingAttributes{
							BingAttributes: &listing_products_v1.BingAttributes{
								BingAttribute: []*listing_products_v1.BingAttribute{
									{
										Name: "attributes/wi_fi",
									},
								},
							},
						},
					},
				},
				IfUnmodifiedSince: &timestamppb.Timestamp{Seconds: 0, Nanos: 0},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
				ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
					PartnerID: "ABC",
				},
				NAPData: &accountgroup.NAPData{
					Country: "US",
				},
				LegacyProductDetails: &accountgroup.LegacyProductDetails{},
			},
			accountGroupMutators: []accountgroup.MutateOption{
				&accountgroup.LegacyProductDetails{
					FaxNumber:  "",
					CellNumber: "",
					Email:      "",
					FieldMask:  []string{"cellNumber", "faxNumber", "email"},
				},
				&accountgroup.RichData{
					TollFreeNumber: "",
					Landmark:       "",
					FieldMask:      []string{"landmark", "inferredAttributes", "tollFreeNumber"},
				},
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID:     "AG-HDV4JKPXFR",
				BingAttributes: `[{"name":"wi_fi"}]`,
			},
			expectedErr: grpc.Errorf(codes.InvalidArgument, "invalid value for attributes/wi_fi"),
		},
		{
			name: "Should by pass HOO updates",
			request: &listing_products_v1.UpdateListingProfileRequest{
				BusinessId: "AG-HDV4JKPXFR",
				UpdateOperations: []*listing_products_v1.UpdateOperation{
					{
						Operation: &listing_products_v1.UpdateOperation_HoursOfOperation{
							HoursOfOperation: &listing_products_v1.HoursOfOperation{
								HoursOfOperation: []*listing_products_v1.HoursOfOperation_Span{
									{
										DayOfWeek: []string{"MONDAY"},
										Opens:     "09:00",
										Closes:    "17:00",
									},
								},
							},
						},
					},
				},
				IfUnmodifiedSince: &timestamppb.Timestamp{Seconds: 0, Nanos: 0},
			},
			expectedAccountGroup: &accountgroup.AccountGroup{
				AccountGroupID: "AG-HDV4JKPXFR",
				ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
					PartnerID: "ABC",
				},
				NAPData: &accountgroup.NAPData{
					Country: "US",
				},
				LegacyProductDetails: &accountgroup.LegacyProductDetails{},
			},
			accountGroupMutators: []accountgroup.MutateOption{
				&accountgroup.LegacyProductDetails{
					FaxNumber:  "",
					CellNumber: "",
					Email:      "",
					FieldMask:  []string{"cellNumber", "faxNumber", "email"},
				},
				&accountgroup.RichData{
					TollFreeNumber: "",
					Landmark:       "",
					FieldMask:      []string{"landmark", "inferredAttributes", "tollFreeNumber"},
				},
			},
			expectedProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-HDV4JKPXFR",
			},
			expectedErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			modifiedSince, _ := ptypes.Timestamp(c.request.GetIfUnmodifiedSince())

			mockListingProfileRepo := listingprofilerepository.NewMockRepository(ctrl)
			mockListingProfileRepo.EXPECT().Upsert(gomock.Any(), c.expectedProfile.BusinessID, modifiedSince, gomock.Any()).Return(c.expectedRepoErr).AnyTimes()
			mockAG := accountgroupwrapper.NewMockServer(ctrl)
			mockAG.EXPECT().Get(gomock.Any(), c.request.GetBusinessId(), gomock.Any()).Return(c.expectedAccountGroup, nil)
			mockAG.EXPECT().UpdateConcurrent(gomock.Any(), c.expectedAccountGroup.AccountGroupID, modifiedSince, gomock.Any()).Return(nil).AnyTimes()

			mockBusinessProfile := businessprofile.NewMockServicer(ctrl)

			mockTurboLister := apiworkflow.NewMockTurboListerAPISubmission(ctrl)
			mockLSP := listingsyncpro.NewMockListingSyncProServiceClientInterface(ctrl)
			mockAddonService := addonattributesservice.NewMockService(ctrl)
			mockYextClient := NewMockYextInterface(ctrl)
			mockNAP := nap.NewMockInterface(ctrl)
			mockPartner := &partner.MockInterface{}
			mockAA := &aasdk.MockPartnerClientInterface{}
			mockTax := cssdk.NewMockTaxonomyClientInterface(ctrl)
			mockGeo := geo.NewMockClient(52.1, -120.2, "EST", nil, nil)
			mockAccountsClient := accountsV2.NewMockAccountsService(ctrl)

			mockAuth := authservice.NewMockInterface(ctrl)
			mockAuth.EXPECT().AccessListingProfileResources(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			externalValidatorServices := listingprofile.NewExternalValidatorServices(mockNAP, mockPartner, mockAA, mockTax, mockGeo)

			mockAccountsClient.EXPECT().GetMultiActivationStatuses(gomock.Any(), &accountsProtos.GetMultiActivationStatusesRequest{
				BusinessIds: []string{c.expectedProfile.BusinessID},
			}).Return(&accountsProtos.GetMultiActivationStatusesResponse{
				ActivationStatuses: []*accountsProtos.GetMultiActivationStatusesResponse_AppsAndAddonsActivationStatuses{
					{
						BusinessId: c.expectedProfile.BusinessID,
						ProductId:  "MS",
						Status:     1,
					},
				},
			}, nil).AnyTimes()

			listingProfileService := listingprofileservice.New(mockListingProfileRepo, mockBusinessProfile, mockAG,
				mockTurboLister, mockLSP, mockAddonService, mockYextClient, nil, nil, nil)

			listingProfileAdapterService := &ListingProfileAdapter{
				service:        listingProfileService,
				evs:            externalValidatorServices,
				accountsClient: mockAccountsClient,
			}

			mockSeoDataService := seodataservice.NewMockService(ctrl)
			mockSeoSettingsService := seosettingsservice.NewMockService(ctrl)

			server := NewListingProfileServer(listingProfileService, mockAuth, listingProfileAdapterService, nil, mockAG, nil, nil, mockSeoDataService, mockSeoSettingsService)

			_, err := server.Update(ctx, c.request)

			if err != nil {
				if c.expectedErr != nil {
					assert.Equal(t, c.expectedErr.Error(), err.Error())
				} else {
					t.Errorf("Unexpected error %v", err)
				}
			}
		})
	}
}

func Test_removeUnOwnedFieldMasksFromLPRichData(t *testing.T) {
	type testCase struct {
		name     string
		input    *vendastatypes.FieldMask
		expected *vendastatypes.FieldMask
	}

	cases := []*testCase{
		{
			name: "It returns valid field mask with owned fields only",
			input: &vendastatypes.FieldMask{
				Paths: []string{"cellNumber", "faxNumber", "email", "landmark", "description"},
			},
			expected: &vendastatypes.FieldMask{
				Paths: []string{"description"},
			},
		},
		{
			name: "It returns empty if there are no owned fields",
			input: &vendastatypes.FieldMask{
				Paths: []string{"cellNumber", "faxNumber", "email", "landmark"},
			},
			expected: &vendastatypes.FieldMask{
				Paths: nil,
			},
		},

		{
			name: "It returns valid field mask with owned fields only even if the masks have different formats",
			input: &vendastatypes.FieldMask{
				Paths: []string{"cell_number", "faxNumber", "email", "landmark", "description"},
			},
			expected: &vendastatypes.FieldMask{
				Paths: []string{"description"},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := removeUnOwnedFieldMasksFromLPRichData(c.input)
			assert.Equal(t, c.expected, actual)
		})
	}
}

func Test_trackUpdatingTaxonomy(t *testing.T) {
	tests := []struct {
		name                string
		updatedTaxIDs       []string
		currentTaxIDs       []string
		updatedVCategoryIDs []string
		currentVCategoryIDs []string
		expectedResult      string
	}{
		{
			name:           "Track with tag api_update_taxonomy while updating vTaxonomyIDs",
			updatedTaxIDs:  []string{"a", "b"},
			currentTaxIDs:  []string{"a"},
			expectedResult: "api_update_taxonomy",
		},
		{
			name:           "Track with tag api_update_taxonomy_no_change if there is no vTaxonomyID change",
			updatedTaxIDs:  []string{"a", "b"},
			currentTaxIDs:  []string{"a", "b"},
			expectedResult: "api_update_taxonomy_no_change",
		},
		{
			name:                "Track with tag api_update_taxonomy if only taxonomies are changed but the vCategoryIDs are the same",
			updatedTaxIDs:       []string{"a", "b"},
			currentTaxIDs:       []string{"a"},
			updatedVCategoryIDs: []string{"category_a"},
			currentVCategoryIDs: []string{"category_a"},
			expectedResult:      "api_update_taxonomy",
		},
		{
			name:                "Track with tag api_update_taxonomy_v_category if both vTaxonomyID and vCategoryIDs are changed",
			updatedTaxIDs:       []string{"a", "b"},
			currentTaxIDs:       []string{"a"},
			updatedVCategoryIDs: []string{"category_a", "category_b"},
			currentVCategoryIDs: []string{"category_a"},
			expectedResult:      "api_update_taxonomy_v_category",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := trackUpdatingTaxonomy(context.Background(), test.updatedTaxIDs, test.currentTaxIDs, test.updatedVCategoryIDs, test.currentVCategoryIDs, "", "")
			if result != test.expectedResult {
				t.Errorf("Expected %v, but got %v", test.expectedResult, result)
			}
		})
	}
}

func TestListingProfileServer_GetMultiAccountGroup(t *testing.T) {
	type fields struct {
		service               listingprofileservice.Interface
		authService           authservice.Interface
		listingProfileAdapter *ListingProfileAdapter
		uberallHttpClient     uberallhttpclient.HttpClienter
		accountGroup          accountgroup.Interface
		bpfsService           businessprofilefieldstatusservice.Service
		accountsService       accounts.Interface
		seoDataService        seodataservice.Service
		seoSettingsService    seosettingsservice.Service
	}
	type args struct {
		ctx     context.Context
		request *listing_products_v1.GetMultiAccountGroupRequest
	}
	tests := []struct {
		name    string
		setup   func(*gomock.Controller, *fields)
		args    args
		want    *listing_products_v1.GetMultiAccountGroupResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "returns consolidated data successfully when all services respond correctly",
			setup: func(ctrl *gomock.Controller, f *fields) {
				f.service.(*listingprofileservice.MockInterface).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, false).Return([]*listingprofilemodel.ListingProfile{
					{
						BusinessID:  "AG-HDV4JKPXFR",
						PartnerID:   "ABC",
						CompanyName: "Test Company",
						Address:     "123 Test St",
						City:        "Test City",
						State:       "TS",
						Zip:         "12345",
						Country:     "US",
						Created:     time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
						Updated:     time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
						Deleted:     time.Date(1, 1, 1, 0, 0, 0, 0, time.UTC),
						RichData:    &listingprofilemodel.RichData{},
						Attributes:  "[{}]",
					},
				}, nil)

				f.authService.(*authservice.MockInterface).EXPECT().AccessListingProfileResources(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				f.accountGroup.(*accountgroupwrapper.MockServer).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, gomock.Any()).Return([]*accountgroup.AccountGroup{
					{
						AccountGroupID: "AG-HDV4JKPXFR",
						NAPData: &accountgroup.NAPData{
							CompanyName: "Test Company",
							Address:     "123 Test St",
							City:        "Test City",
							State:       "TS",
							Zip:         "12345",
							Country:     "US",
							Website:     "https://test.com",
							WorkNumber:  []string{"555-1234"},
						},
						ExternalIdentifiers: &accountgroup.ExternalIdentifiers{
							PartnerID: "ABC",
							Origin:    "test",
						},
						RichData: &accountgroup.RichData{
							Description: "Test description",
						},
					},
				}, nil)

				f.accountsService.(*accounts.MockAccountsInterface).EXPECT().ListAppsAndAddonsActivationStatusesForBusiness(gomock.Any(), "AG-HDV4JKPXFR").Return(&accounts_v1.ListAppsAndAddonsActivationsStatusesForBusinessResponse{
					ActivationStatuses: []*accounts_v1.ListAppsAndAddonsActivationsStatusesForBusinessResponse_AppsAndAddonsActivationStatuses{
						{
							Status: accounts_v1.AppAndAddonActivationStatus_ACTIVATED,
						},
					},
				}, nil)

				f.seoDataService.(*seodataservice.MockService).EXPECT().GetActiveKeywordAddons(gomock.Any(), "AG-HDV4JKPXFR").Return([]*seodataservice.AddOn{
					{
						BusinessID: "AG-HDV4JKPXFR",
						AddonID:    "SEO_ADDON_1",
						AppID:      "SEO_APP_1",
					},
				}, nil)

				f.seoSettingsService.(*seosettingsservice.MockService).EXPECT().Get(gomock.Any(), "AG-HDV4JKPXFR").Return(&seodatasettingsmodel.SEOSettings{
					LocalSearchRadius:   10.5,
					FavoriteKeywords:    []string{"test", "keyword"},
					IsFullSearchEnabled: true,
				}, nil)
			},
			args: args{
				ctx: context.Background(),
				request: &listing_products_v1.GetMultiAccountGroupRequest{
					BusinessIds: []string{"AG-HDV4JKPXFR"},
					ProjectionFilter: &listing_products_v1.ProjectionFilter{
						NapData:  true,
						RichData: true,
					},
					LanguageCode: "en",
				},
			},
			want: &listing_products_v1.GetMultiAccountGroupResponse{
				ConsolidatedData: []*listing_products_v1.ConsolidatedDataContainer{
					{
						BusinessId: "AG-HDV4JKPXFR",
						ListingProfile: &listing_products_v1.ListingProfile{
							BusinessId: "AG-HDV4JKPXFR",
							Created:    &timestamppb.Timestamp{Seconds: **********, Nanos: 0},
							Updated:    &timestamppb.Timestamp{Seconds: **********, Nanos: 0},
							Deleted:    nil,
							Version:    0,
							NapData: &listing_products_v1.Location{
								CompanyName: "Test Company",
								Address:     "123 Test St",
								City:        "Test City",
								State:       "TS",
								Zip:         "12345",
								Country:     "US",
							},
							RichData: &listing_products_v1.RichData{
								PaymentMethods: []listing_products_v1.RichData_PaymentMethods{},
							},
						},
						AccountGroup: &listing_products_v1.AccountGroupData{
							AccountGroupId: "AG-HDV4JKPXFR",
							NapData: &listing_products_v1.Location{
								CompanyName: "Test Company",
								Address:     "123 Test St",
								City:        "Test City",
								State:       "TS",
								Zip:         "12345",
								Country:     "US",
								Website:     "https://test.com",
								WorkNumber:  []string{"555-1234"},
							},
							ExternalIdentifiers: &listing_products_v1.ExternalIdentifiers{
								PartnerId: "ABC",
								Origin:    "test",
							},
							RichData: &listing_products_v1.RichData{
								Description: "Test description",
							},
						},
						ActivationStatus: &listing_products_v1.ActivationStatusData{
							ActivationStatuses: []listing_products_v1.AppAndAddonActivationStatus{
								listing_products_v1.AppAndAddonActivationStatus_ACTIVATED,
							},
						},
						SeoAddons: &listing_products_v1.SEOAddonData{
							Addons: []*listing_products_v1.SEOAddonActivation{
								{
									BusinessId: "AG-HDV4JKPXFR",
									AddonId:    "SEO_ADDON_1",
									AppId:      "SEO_APP_1",
								},
							},
						},
						SeoSettings: &listing_products_v1.SEOSettingsData{
							LocalSearchRadius:   10.5,
							FavoriteKeywords:    []string{"test", "keyword"},
							IsFullSearchEnabled: true,
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "returns error when business IDs list is empty",
			setup: func(ctrl *gomock.Controller, f *fields) {
			},
			args: args{
				ctx: context.Background(),
				request: &listing_products_v1.GetMultiAccountGroupRequest{
					BusinessIds: []string{},
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
		{
			name: "returns error when business IDs list contains empty string",
			setup: func(ctrl *gomock.Controller, f *fields) {
			},
			args: args{
				ctx: context.Background(),
				request: &listing_products_v1.GetMultiAccountGroupRequest{
					BusinessIds: []string{"AG-HDV4JKPXFR", ""},
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
		{
			name: "returns error when listing profile service fails with database error",
			setup: func(ctrl *gomock.Controller, f *fields) {
				f.service.(*listingprofileservice.MockInterface).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, false).Return(nil, verrors.New(verrors.Internal, "database error"))
			},
			args: args{
				ctx: context.Background(),
				request: &listing_products_v1.GetMultiAccountGroupRequest{
					BusinessIds: []string{"AG-HDV4JKPXFR"},
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
		{
			name: "returns error when account group service fails after successful listing profile retrieval",
			setup: func(ctrl *gomock.Controller, f *fields) {
				f.service.(*listingprofileservice.MockInterface).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, false).Return([]*listingprofilemodel.ListingProfile{
					{
						BusinessID: "AG-HDV4JKPXFR",
						PartnerID:  "ABC",
					},
				}, nil)

				f.accountGroup.(*accountgroupwrapper.MockServer).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, gomock.Any()).Return(nil, verrors.New(verrors.Internal, "account group error"))
			},
			args: args{
				ctx: context.Background(),
				request: &listing_products_v1.GetMultiAccountGroupRequest{
					BusinessIds: []string{"AG-HDV4JKPXFR"},
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
		{
			name: "returns permission denied error when user lacks access to listing profile resources",
			setup: func(ctrl *gomock.Controller, f *fields) {
				f.service.(*listingprofileservice.MockInterface).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, false).Return([]*listingprofilemodel.ListingProfile{
					{
						BusinessID: "AG-HDV4JKPXFR",
						PartnerID:  "ABC",
					},
				}, nil)

				f.authService.(*authservice.MockInterface).EXPECT().AccessListingProfileResources(gomock.Any(), gomock.Any(), gomock.Any()).Return(verrors.New(verrors.PermissionDenied, "access denied"))

				f.accountGroup.(*accountgroupwrapper.MockServer).EXPECT().GetMulti(gomock.Any(), []string{"AG-HDV4JKPXFR"}, gomock.Any()).Return([]*accountgroup.AccountGroup{
					{
						AccountGroupID: "AG-HDV4JKPXFR",
					},
				}, nil)
			},
			args: args{
				ctx: context.Background(),
				request: &listing_products_v1.GetMultiAccountGroupRequest{
					BusinessIds: []string{"AG-HDV4JKPXFR"},
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			fields := fields{
				service:            listingprofileservice.NewMockInterface(ctrl),
				authService:        authservice.NewMockInterface(ctrl),
				accountGroup:       accountgroupwrapper.NewMockServer(ctrl),
				accountsService:    accounts.NewMockAccountsInterface(ctrl),
				seoDataService:     seodataservice.NewMockService(ctrl),
				seoSettingsService: seosettingsservice.NewMockService(ctrl),
			}

			if tt.setup != nil {
				tt.setup(ctrl, &fields)
			}

			l := &ListingProfileServer{
				service:               fields.service,
				authService:           fields.authService,
				listingProfileAdapter: fields.listingProfileAdapter,
				uberallHttpClient:     fields.uberallHttpClient,
				accountGroup:          fields.accountGroup,
				bpfsService:           fields.bpfsService,
				accountsService:       fields.accountsService,
				seoDataService:        fields.seoDataService,
				seoSettingsService:    fields.seoSettingsService,
			}
			got, err := l.GetMultiAccountGroup(tt.args.ctx, tt.args.request)
			if !tt.wantErr(t, err, fmt.Sprintf("GetConsolidatedData(%v, %v)", tt.args.ctx, tt.args.request)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetConsolidatedData(%v, %v)", tt.args.ctx, tt.args.request)
		})
	}
}
